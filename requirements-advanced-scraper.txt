# Advanced PDF Scraper Dependencies
# Install with: pip install -r requirements-advanced-scraper.txt

# Core async libraries
aiohttp>=3.8.0
aiofiles>=0.8.0
asyncio-throttle>=1.0.0

# Browser automation
playwright>=1.30.0

# Retry and error handling
tenacity>=8.0.0

# Existing dependencies (should already be installed)
requests>=2.25.0
beautifulsoup4>=4.9.0
python-dotenv>=0.19.0
selenium>=4.0.0
urllib3>=1.26.0

# Optional: For better logging and monitoring
rich>=12.0.0
tqdm>=4.64.0
