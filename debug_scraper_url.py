#!/usr/bin/env python3
"""
Debug script to check ScraperAPI URL generation
"""

import urllib.parse
from dotenv import load_dotenv
import os

load_dotenv()
SCRAPER_API_KEY = os.getenv("SCRAPER_API_KEY")

def test_url_generation():
    """Test URL generation for ScraperAPI"""
    
    test_url = "https://www.seilenergy.com/assets/Document/SEIL Annual Report 2023-24.pdf"
    
    print(f"Original URL: {test_url}")
    
    # Test URL encoding
    encoded_url = urllib.parse.quote(test_url, safe=':/?#[]@!$&\'()*+,;=')
    print(f"Encoded URL: {encoded_url}")
    
    # Generate ScraperAPI URL
    scraper_url = f"http://api.scraperapi.com?api_key={SCRAPER_API_KEY}&url={encoded_url}"
    print(f"ScraperAPI URL: {scraper_url}")
    
    # Test with requests
    import requests
    try:
        print("\n🔍 Testing ScraperAPI request...")
        response = requests.get(scraper_url, timeout=10)
        print(f"Status code: {response.status_code}")
        print(f"Content length: {len(response.content)}")
        print(f"Content type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            print("✅ ScraperAPI request successful!")
        else:
            print(f"❌ ScraperAPI request failed: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_url_generation()
