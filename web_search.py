# web_search.py
import os
import requests
import urllib3
from pathlib import Path
from bs4 import BeautifulSoup
from dotenv import load_dotenv
# Load API key
load_dotenv()
SCRAPER_API_KEY = os.getenv("SCRAPER_API_KEY")
SCRAPER_API_URL = "https://api.scraperapi.com/structured/google/search"
HEADERS = {"User-Agent": "Mozilla/5.0"}
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# Function to search for the PDF URL using ScraperAPI (for direct download)
def search_pdf_url(query: str):
    params = {
        "api_key": SCRAPER_API_KEY,
        "query": query,
        "num": 5
    }
    try:
        response = requests.get(SCRAPER_API_URL, params=params, headers=HEADERS)
        response.raise_for_status()
        results = response.json().get("organic_results", [])
        # Get the first valid PDF URL
        for result in results:
            link = result.get("link", "")
            if link.lower().endswith(".pdf"):
                return link
    except Exception as e:
        print(f"❌ Error during PDF search: {e}")
    return None
# Function to download a PDF from a URL
def download_pdf(url: str, dest_path: Path):
    try:
        r = requests.get(url, stream=True, timeout=15, verify=False)
        r.raise_for_status()
        with open(dest_path, "wb") as f:
            for chunk in r.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        print(f"✅ Downloaded: {dest_path}")
    except Exception as e:
        print(f"❌ Failed to download {url}: {e}")
# Function to search and scrape PDF links from an HTML page using ScraperAPI
def download_pdfs_from_url(query: str, plant_name: str, limit_no_reports: int = None):
    params = {
        "api_key": SCRAPER_API_KEY,
        "query": query,
        "num": 5
    }
    try:
        response = requests.get(SCRAPER_API_URL, params=params, headers=HEADERS)
        response.raise_for_status()
        results = response.json().get("organic_results", [])
        # Get the first valid URL to scrape
        for result in results:
            url = result.get("link", "")
            if url.startswith("http"):
                base_url = url.split("/")[0] + "//" + url.split("/")[2]
                downloads_dir = os.path.join(os.getcwd(), "downloads", plant_name)
                os.makedirs(downloads_dir, exist_ok=True)
                try:
                    page_response = requests.get(url, timeout=10)
                    page_response.raise_for_status()
                except Exception as e:
                    print(f"❌ Failed to retrieve page: {url} – {e}")
                    continue
                soup = BeautifulSoup(page_response.text, "html.parser")
                pdf_links = [a['href'] for a in soup.find_all('a', href=True) if a['href'].lower().endswith('.pdf')]
                count = 0
                for link in pdf_links:
                    if limit_no_reports is not None and count >= limit_no_reports:
                        break
                    if link.startswith("/"):
                        link = base_url + link
                    elif not link.startswith("http"):
                        link = base_url + "/" + link
                    filename = os.path.basename(link)
                    filepath = os.path.join(downloads_dir, filename)
                    print(f"⬇️ Downloading {link} to {filepath}")
                    try:
                        file_response = requests.get(link, timeout=15)
                        with open(filepath, "wb") as f:
                            f.write(file_response.content)
                        print(f"✅ Saved: {filename}")
                        count += 1
                    except Exception as download_err:
                        print(f"❌ Failed to download {link}: {download_err}")
                return
    except Exception as e:
        print(f"❌ Error during URL search for page scraping: {e}")
# Main runner function
def run_downloader(plant_name: str, from_year: int, to_year: int):
    folder_name = plant_name.replace(" ", "_").replace('"', '')
    save_dir = Path("downloads") / folder_name
    save_dir.mkdir(parents=True, exist_ok=True)
    any_pdf_downloaded = False
    for year in range(from_year, to_year + 1):
        print(f"\n🔍 Searching: {plant_name} - {year}")
        # Construct query for direct PDF download
        query_pdf = f'"{plant_name}" power plant annual report {year} filetype:pdf'
        # Step 1: Try to find the PDF URL using the query
        pdf_url = search_pdf_url(query_pdf)
        if pdf_url:
            # Step 2: If PDF is found, download it
            pdf_path = save_dir / f"{year}.pdf"
            download_pdf(pdf_url, pdf_path)
            any_pdf_downloaded = True
        else:
            print(f"⚠️ No direct PDF found for {year}.")
    # Step 3: If no PDFs found from direct search, try scraping
    if not any_pdf_downloaded:
        print("⚠️ Trying page scraping...")
        query_page = f'"{plant_name}" Investor Relations Annual Report'
        before_scrape_files = set(os.listdir(save_dir))  # Record existing files
        download_pdfs_from_url(query_page, folder_name, 5)
        after_scrape_files = set(os.listdir(save_dir))  # Check after scraping
        if before_scrape_files == after_scrape_files:
            print("❌ No annual reports found for the provided power plant.")
        else:
            print("✅ Found reports via page scraping.")
    else:
        print("✅ Finished downloading reports from direct PDF search.")