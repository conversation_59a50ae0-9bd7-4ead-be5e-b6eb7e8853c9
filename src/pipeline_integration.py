#!/usr/bin/env python3
"""
Pipeline Integration Module

End-to-end function for power plant PDF downloading.
Input: Plant name → Output: Downloaded PDFs (no user interaction required)

This module combines all search and download functionality into a single
pipeline-ready function for automated processing.
"""

import os
import sys
import time
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import core components
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper
from agent.content_analyzer import ContentAnalyzer
from agent.graph import power_plant_graph
from agent.configuration import Configuration

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PowerPlantPDFPipeline:
    """End-to-end pipeline for power plant PDF downloading"""
    
    def __init__(self, download_dir: str = "./annual_reports", max_search_results: int = 10):
        """
        Initialize the pipeline
        
        Args:
            download_dir: Directory to save downloaded PDFs
            max_search_results: Maximum number of search results to process
        """
        self.download_dir = download_dir
        self.max_search_results = max_search_results
        
        # Initialize components
        self.scraper = ScraperAPIPDFScraper(download_dir=download_dir)
        self.content_analyzer = ContentAnalyzer()
        self.web_search = WebSearchAgent()
        
        # Create download directory
        Path(download_dir).mkdir(parents=True, exist_ok=True)
        
    def search_power_plant_urls(self, plant_name: str) -> List[str]:
        """
        Search for power plant URLs
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            List of relevant URLs
        """
        try:
            print(f"🔍 Searching for {plant_name} power plant...")
            
            # Perform web search
            search_results = self.web_search.search_power_plant(
                plant_name, 
                max_results=self.max_search_results
            )
            
            if not search_results:
                print(f"❌ No search results found for {plant_name}")
                return []
            
            print(f"📊 Found {len(search_results)} search results")
            
            # Extract URLs from search results
            urls = []
            for result in search_results:
                if isinstance(result, dict) and 'url' in result:
                    urls.append(result['url'])
                elif isinstance(result, str):
                    urls.append(result)
            
            # Remove duplicates while preserving order
            unique_urls = []
            seen = set()
            for url in urls:
                if url not in seen:
                    unique_urls.append(url)
                    seen.add(url)
            
            print(f"🔗 Found {len(unique_urls)} unique URLs")
            return unique_urls
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            logger.error(f"Search failed for {plant_name}: {e}")
            return []
    
    def analyze_and_filter_urls(self, urls: List[str], plant_name: str) -> List[str]:
        """
        Analyze URLs and filter for relevance
        
        Args:
            urls: List of URLs to analyze
            plant_name: Name of the power plant
            
        Returns:
            List of relevant URLs
        """
        try:
            print(f"🧠 Analyzing {len(urls)} URLs for relevance...")
            
            relevant_urls = []
            
            for i, url in enumerate(urls, 1):
                try:
                    print(f"   Analyzing URL {i}/{len(urls)}: {url[:60]}...")
                    
                    # Analyze content relevance
                    analysis = self.content_analyzer.analyze_url_content(url)
                    
                    if analysis and analysis.get('is_relevant', False):
                        score = analysis.get('relevance_score', 0)
                        print(f"   ✅ Relevant (score: {score})")
                        relevant_urls.append(url)
                    else:
                        print(f"   ❌ Not relevant")
                        
                except Exception as e:
                    print(f"   ⚠️ Analysis failed: {e}")
                    # Include URL anyway if analysis fails
                    relevant_urls.append(url)
                    continue
            
            print(f"✅ Selected {len(relevant_urls)} relevant URLs")
            return relevant_urls
            
        except Exception as e:
            print(f"❌ URL analysis failed: {e}")
            logger.error(f"URL analysis failed: {e}")
            # Return original URLs if analysis fails
            return urls
    
    def download_pdfs_from_urls(self, urls: List[str], plant_name: str) -> List[str]:
        """
        Download PDFs from the provided URLs
        
        Args:
            urls: List of URLs to scrape for PDFs
            plant_name: Name of the power plant
            
        Returns:
            List of downloaded file paths
        """
        try:
            print(f"📄 Starting PDF download from {len(urls)} URLs...")
            
            # Use the integrated scraper (includes smart download + advanced fallback)
            downloaded_files = self.scraper.scrape_annual_reports(urls, plant_name)
            
            print(f"✅ Download completed: {len(downloaded_files)} files")
            return downloaded_files
            
        except Exception as e:
            print(f"❌ PDF download failed: {e}")
            logger.error(f"PDF download failed: {e}")
            return []
    
    def process_power_plant(self, plant_name: str) -> Dict[str, Any]:
        """
        End-to-end processing: Plant name → Downloaded PDFs
        
        Args:
            plant_name: Name of the power plant to process
            
        Returns:
            Dictionary with processing results:
            {
                'plant_name': str,
                'urls_found': List[str],
                'relevant_urls': List[str], 
                'pdfs_downloaded': List[str],
                'total_files': int,
                'processing_time': float,
                'errors': List[str],
                'success': bool
            }
        """
        start_time = time.time()
        
        result = {
            'plant_name': plant_name,
            'urls_found': [],
            'relevant_urls': [],
            'pdfs_downloaded': [],
            'total_files': 0,
            'processing_time': 0.0,
            'errors': [],
            'success': False
        }
        
        try:
            print(f"\n🚀 Starting end-to-end processing for: {plant_name}")
            print("=" * 60)
            
            # Step 1: Search for URLs
            urls = self.search_power_plant_urls(plant_name)
            result['urls_found'] = urls
            
            if not urls:
                result['errors'].append("No URLs found in search")
                return result
            
            # Step 2: Analyze and filter URLs
            relevant_urls = self.analyze_and_filter_urls(urls, plant_name)
            result['relevant_urls'] = relevant_urls
            
            if not relevant_urls:
                result['errors'].append("No relevant URLs found")
                return result
            
            # Step 3: Download PDFs
            downloaded_files = self.download_pdfs_from_urls(relevant_urls, plant_name)
            result['pdfs_downloaded'] = downloaded_files
            result['total_files'] = len(downloaded_files)
            
            # Calculate processing time
            end_time = time.time()
            result['processing_time'] = end_time - start_time
            
            # Determine success
            result['success'] = len(downloaded_files) > 0
            
            # Print summary
            print("\n📊 PROCESSING SUMMARY")
            print("=" * 60)
            print(f"Plant: {plant_name}")
            print(f"URLs found: {len(urls)}")
            print(f"Relevant URLs: {len(relevant_urls)}")
            print(f"PDFs downloaded: {len(downloaded_files)}")
            print(f"Processing time: {result['processing_time']:.2f} seconds")
            print(f"Success: {'✅ Yes' if result['success'] else '❌ No'}")
            
            if downloaded_files:
                print(f"\n📁 Downloaded files:")
                for file_path in downloaded_files:
                    filename = os.path.basename(file_path)
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                        print(f"   📄 {filename} ({file_size:.2f} MB)")
                    else:
                        print(f"   ❌ {filename} (file not found)")
            
            return result
            
        except Exception as e:
            error_msg = f"Pipeline processing failed: {e}"
            result['errors'].append(error_msg)
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            
            # Calculate processing time even on failure
            end_time = time.time()
            result['processing_time'] = end_time - start_time
            
            return result

# Convenience function for direct use
def download_power_plant_pdfs(plant_name: str, download_dir: str = "./annual_reports") -> Dict[str, Any]:
    """
    Convenience function for end-to-end power plant PDF downloading
    
    Args:
        plant_name: Name of the power plant
        download_dir: Directory to save PDFs
        
    Returns:
        Processing results dictionary
    """
    pipeline = PowerPlantPDFPipeline(download_dir=download_dir)
    return pipeline.process_power_plant(plant_name)

# Batch processing function
def download_multiple_power_plants(plant_names: List[str], download_dir: str = "./annual_reports") -> List[Dict[str, Any]]:
    """
    Process multiple power plants in batch
    
    Args:
        plant_names: List of power plant names
        download_dir: Directory to save PDFs
        
    Returns:
        List of processing results for each plant
    """
    pipeline = PowerPlantPDFPipeline(download_dir=download_dir)
    results = []
    
    print(f"🚀 Starting batch processing of {len(plant_names)} power plants")
    print("=" * 80)
    
    for i, plant_name in enumerate(plant_names, 1):
        print(f"\n📍 Processing plant {i}/{len(plant_names)}: {plant_name}")
        result = pipeline.process_power_plant(plant_name)
        results.append(result)
        
        # Brief pause between plants to be respectful to servers
        if i < len(plant_names):
            time.sleep(2)
    
    # Print batch summary
    print(f"\n🎯 BATCH PROCESSING SUMMARY")
    print("=" * 80)
    total_files = sum(r['total_files'] for r in results)
    successful_plants = sum(1 for r in results if r['success'])
    total_time = sum(r['processing_time'] for r in results)
    
    print(f"Plants processed: {len(plant_names)}")
    print(f"Successful plants: {successful_plants}")
    print(f"Total PDFs downloaded: {total_files}")
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Average time per plant: {total_time/len(plant_names):.2f} seconds")
    
    return results

if __name__ == "__main__":
    # Example usage
    if len(sys.argv) > 1:
        plant_name = " ".join(sys.argv[1:])
        result = download_power_plant_pdfs(plant_name)
        
        if result['success']:
            print(f"\n🎉 Successfully downloaded {result['total_files']} PDFs for {plant_name}")
        else:
            print(f"\n❌ Failed to download PDFs for {plant_name}")
            if result['errors']:
                print("Errors:", result['errors'])
    else:
        print("Usage: python pipeline_integration.py <plant_name>")
        print("Example: python pipeline_integration.py 'SEIL Energy'")
