#!/usr/bin/env python3
"""
Example usage of the Integrated Power Plant Scraper

This script demonstrates how to use the integrated scraper programmatically
without user interaction.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from integrated_power_plant_scraper import IntegratedPowerPlantScraper

# Load environment variables
load_dotenv()


def run_automated_search(plant_name: str, download_dir: str = "./downloads"):
    """Run automated search for a power plant without user interaction.
    
    Args:
        plant_name: Name of the power plant to search for
        download_dir: Directory to save downloaded PDFs
        
    Returns:
        Dictionary with search results
    """
    print(f"🤖 Running automated search for: {plant_name}")
    
    # Check for required environment variables
    required_keys = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_keys = [key for key in required_keys if not os.getenv(key)]
    
    if missing_keys:
        print("❌ Error: Missing required environment variables:")
        for key in missing_keys:
            print(f"   - {key}")
        return {"error": f"Missing environment variables: {missing_keys}"}
    
    # Initialize the integrated scraper
    scraper = IntegratedPowerPlantScraper(download_dir=download_dir)
    
    # Run the integrated search and validation
    results = scraper.run_integrated_search_and_validation(plant_name)
    
    return results


def main():
    """Example usage of the automated scraper."""
    
    # Example 1: Search for San Miguel Corporation
    print("Example 1: San Miguel Corporation")
    print("=" * 40)
    
    results1 = run_automated_search("San Miguel Corporation")
    
    if results1.get("valid_pdfs"):
        print(f"✅ Found {len(results1['valid_pdfs'])} valid consolidated annual reports")
    else:
        print("❌ No valid consolidated annual reports found")
    
    print("\n" + "="*60 + "\n")
    
    # Example 2: Search for Pirkey Power Plant
    print("Example 2: Pirkey Power Plant")
    print("=" * 40)
    
    results2 = run_automated_search("Pirkey")
    
    if results2.get("valid_pdfs"):
        print(f"✅ Found {len(results2['valid_pdfs'])} valid consolidated annual reports")
    else:
        print("❌ No valid consolidated annual reports found")
    
    # Print summary
    print("\n" + "="*60)
    print("📊 SUMMARY")
    print("="*60)
    
    total_valid = len(results1.get("valid_pdfs", [])) + len(results2.get("valid_pdfs", []))
    total_downloaded = results1.get("total_downloaded", 0) + results2.get("total_downloaded", 0)
    
    print(f"Total PDFs downloaded: {total_downloaded}")
    print(f"Total valid consolidated reports: {total_valid}")
    print(f"Success rate: {(total_valid/total_downloaded*100):.1f}%" if total_downloaded > 0 else "No PDFs downloaded")


if __name__ == "__main__":
    main()
