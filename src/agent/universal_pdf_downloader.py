"""
Universal PDF Downloader Module for Complex Power Plant Websites

This module provides advanced PDF downloading capabilities using Playwright
for JavaScript-heavy websites that the basic scraper cannot handle.
"""

import asyncio
import aiohttp
import aiofiles
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse
import os
import logging
from pathlib import Path
import time
import json
from typing import List, Dict, Optional
import re
from tenacity import retry, stop_after_attempt, wait_exponential
import hashlib

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UniversalPDFDownloader:
    def __init__(self, download_dir: str = "./annual_reports", max_concurrent: int = 5):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.max_concurrent = max_concurrent
        self.session = None
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Common PDF download patterns
        self.pdf_patterns = [
            r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
            r'src=["\']([^"\']*\.pdf[^"\']*)["\']',
            r'data-url=["\']([^"\']*\.pdf[^"\']*)["\']',
            r'data-href=["\']([^"\']*\.pdf[^"\']*)["\']',
            r'onclick=["\'][^"\']*window\.open\(["\']([^"\']*\.pdf[^"\']*)["\']',
        ]
        
        # Common selectors for PDF links
        self.pdf_selectors = [
            'a[href*=".pdf"]',
            'a[href*="download"]',
            'a[href*="report"]',
            'a[href*="annual"]',
            'button[onclick*=".pdf"]',
            'div[data-url*=".pdf"]',
            'iframe[src*=".pdf"]',
            'embed[src*=".pdf"]',
            'object[data*=".pdf"]',
            '.download-link',
            '.pdf-link',
            '.report-link',
            '[class*="pdf"]',
            '[class*="download"]',
            '[class*="report"]'
        ]

    async def __aenter__(self):
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=300)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def generate_filename(self, url: str, plant_name: str = None) -> str:
        """Generate unique filename for PDF"""
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        if not filename or not filename.endswith('.pdf'):
            # Create filename from URL hash
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            filename = f"{plant_name or 'plant'}_{url_hash}.pdf"
        
        # Sanitize filename
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return filename

    async def extract_pdf_links_static(self, html_content: str, base_url: str) -> List[str]:
        """Extract PDF links from static HTML content"""
        pdf_links = []
        
        for pattern in self.pdf_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                full_url = urljoin(base_url, match)
                if full_url not in pdf_links:
                    pdf_links.append(full_url)
        
        return pdf_links

    async def extract_pdf_links_dynamic(self, page, base_url: str) -> List[str]:
        """Extract PDF links using browser automation"""
        pdf_links = []
        
        try:
            # Wait for page to load completely
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            # Try multiple selectors to find PDF links
            for selector in self.pdf_selectors:
                try:
                    elements = await page.locator(selector).all()
                    for element in elements:
                        # Try different attributes
                        for attr in ['href', 'src', 'data-url', 'data-href']:
                            try:
                                url = await element.get_attribute(attr)
                                if url and ('.pdf' in url.lower() or 'download' in url.lower()):
                                    full_url = urljoin(base_url, url)
                                    if full_url not in pdf_links:
                                        pdf_links.append(full_url)
                            except:
                                continue
                                
                        # Check onclick events
                        try:
                            onclick = await element.get_attribute('onclick')
                            if onclick and '.pdf' in onclick:
                                # Extract URL from onclick
                                url_match = re.search(r'["\']([^"\']*\.pdf[^"\']*)["\']', onclick)
                                if url_match:
                                    full_url = urljoin(base_url, url_match.group(1))
                                    if full_url not in pdf_links:
                                        pdf_links.append(full_url)
                        except:
                            continue
                            
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            # Handle JavaScript-generated download links
            try:
                # Execute JavaScript to find hidden download URLs
                js_links = await page.evaluate("""
                    () => {
                        const links = [];
                        
                        // Check for data attributes
                        document.querySelectorAll('[data-url], [data-href], [data-download]').forEach(el => {
                            const url = el.dataset.url || el.dataset.href || el.dataset.download;
                            if (url && url.includes('.pdf')) {
                                links.push(url);
                            }
                        });
                        
                        // Check for embedded PDFs
                        document.querySelectorAll('iframe, embed, object').forEach(el => {
                            const src = el.src || el.data;
                            if (src && src.includes('.pdf')) {
                                links.push(src);
                            }
                        });
                        
                        return links;
                    }
                """)
                
                for link in js_links:
                    full_url = urljoin(base_url, link)
                    if full_url not in pdf_links:
                        pdf_links.append(full_url)
                        
            except Exception as e:
                logger.debug(f"JavaScript evaluation failed: {e}")
            
        except Exception as e:
            logger.error(f"Dynamic link extraction failed: {e}")
        
        return pdf_links

    async def handle_special_download_mechanisms(self, page, base_url: str) -> List[str]:
        """Handle special download mechanisms like forms, AJAX, etc."""
        pdf_links = []
        
        try:
            # Handle download buttons that trigger JavaScript
            download_buttons = await page.locator('button, a').all()
            for button in download_buttons[:5]:  # Limit to first 5 to avoid too many clicks
                try:
                    text = await button.inner_text()
                    if text and any(keyword in text.lower() for keyword in ['download', 'pdf', 'report', 'annual']):
                        # Set up download handler before clicking
                        downloads = []
                        def handle_download(download):
                            downloads.append(download)
                        
                        page.on('download', handle_download)
                        
                        # Click and wait briefly for download
                        try:
                            await button.click(timeout=5000)
                            await page.wait_for_timeout(2000)  # Wait 2 seconds for download to start
                            
                            for download in downloads:
                                pdf_links.append(await download.url())
                                
                        except Exception as click_e:
                            logger.debug(f"Button click failed: {click_e}")
                            continue
                            
                except Exception as e:
                    logger.debug(f"Button handling failed: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Special mechanism handling failed: {e}")
        
        return pdf_links

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def download_pdf(self, url: str, filename: str) -> Optional[str]:
        """Download PDF file with retry logic"""
        try:
            async with self.semaphore:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'application/pdf' in content_type or url.endswith('.pdf'):
                            filepath = self.download_dir / filename
                            
                            async with aiofiles.open(filepath, 'wb') as f:
                                async for chunk in response.content.iter_chunked(8192):
                                    await f.write(chunk)
                            
                            logger.info(f"Downloaded: {filename}")
                            return str(filepath)
                    else:
                        logger.warning(f"Failed to download {url}: HTTP {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Download failed for {url}: {e}")
            return None

    async def process_single_url(self, plant_url: str, plant_name: str = None) -> Dict:
        """Process a single URL and extract/download PDFs"""
        result = {
            'plant_url': plant_url,
            'plant_name': plant_name,
            'pdf_links': [],
            'downloaded_files': [],
            'errors': []
        }
        
        try:
            print(f"🔍 Advanced scraping: {plant_url}")
            
            # Try dynamic scraping with Playwright
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                )
                page = await context.new_page()
                
                # Set up download handler
                downloads = []
                def handle_download(download):
                    downloads.append(download)
                
                page.on('download', handle_download)
                
                try:
                    await page.goto(plant_url, wait_until='networkidle', timeout=30000)
                    
                    # Extract dynamic links
                    dynamic_links = await self.extract_pdf_links_dynamic(page, plant_url)
                    result['pdf_links'].extend(dynamic_links)
                    
                    # Handle special download mechanisms
                    special_links = await self.handle_special_download_mechanisms(page, plant_url)
                    result['pdf_links'].extend(special_links)
                    
                    # Handle direct downloads
                    for download in downloads:
                        filename = self.generate_filename(plant_url, plant_name)
                        plant_dir = self.download_dir / plant_name.replace(' ', '_') if plant_name else self.download_dir
                        plant_dir.mkdir(exist_ok=True)
                        filepath = plant_dir / filename
                        await download.save_as(filepath)
                        result['downloaded_files'].append(str(filepath))
                        print(f"✅ Direct download saved: {filename}")
                        
                except Exception as e:
                    result['errors'].append(f"Browser automation failed: {e}")
                finally:
                    await browser.close()
            
            # Download all found PDF links
            if result['pdf_links']:
                print(f"📄 Found {len(result['pdf_links'])} PDF links, downloading...")
                download_tasks = []
                for pdf_url in result['pdf_links']:
                    filename = self.generate_filename(pdf_url, plant_name)
                    # Create plant-specific directory
                    plant_dir = self.download_dir / plant_name.replace(' ', '_') if plant_name else self.download_dir
                    plant_dir.mkdir(exist_ok=True)
                    task = self.download_pdf(pdf_url, plant_dir / filename)
                    download_tasks.append(task)
                
                downloaded_files = await asyncio.gather(*download_tasks, return_exceptions=True)
                for file_path in downloaded_files:
                    if isinstance(file_path, str):
                        result['downloaded_files'].append(file_path)
                        print(f"✅ Downloaded: {os.path.basename(file_path)}")
                    elif isinstance(file_path, Exception):
                        result['errors'].append(str(file_path))
        
        except Exception as e:
            result['errors'].append(f"Processing failed: {e}")
        
        return result

# Synchronous wrapper for integration with existing code
def download_pdfs_advanced(urls: List[str], plant_name: str, download_dir: str = "./annual_reports") -> List[str]:
    """Synchronous wrapper for the async PDF downloader"""
    
    async def run_download():
        downloaded_files = []
        
        async with UniversalPDFDownloader(download_dir=download_dir, max_concurrent=3) as downloader:
            for url in urls:
                result = await downloader.process_single_url(url, plant_name)
                downloaded_files.extend(result['downloaded_files'])
                
                if result['errors']:
                    print(f"⚠️ Errors for {url}: {result['errors']}")
        
        return downloaded_files
    
    # Run the async function
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(run_download())
