#!/usr/bin/env python3
"""
Test script to verify the advanced scraper is working
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper

def test_advanced_scraper():
    """Test the advanced scraper with the San Miguel URLs"""
    
    # URLs from the user's output
    test_urls = [
        "https://www.sanmiguel.com.ph/corporate/investor-relations/financial-performance/annual-reports"
    ]
    
    plant_name = "San Miguel Corporation"
    
    print("🧪 Testing advanced scraper after Playwright installation...")
    
    # Initialize scraper
    scraper = ScraperAPIPDFScraper(download_dir="./test_annual_reports_2")
    
    # Test the scrape_annual_reports method
    print("\n🔍 Testing scrape_annual_reports method...")
    downloaded_files = scraper.scrape_annual_reports(test_urls, plant_name)
    
    print(f"\n📊 Final result: {len(downloaded_files)} files downloaded")
    for file_path in downloaded_files:
        print(f"  ✅ {os.path.basename(file_path)}")
    
    return len(downloaded_files) > 0

if __name__ == "__main__":
    success = test_advanced_scraper()
    if success:
        print("\n🎉 Advanced scraper is working!")
    else:
        print("\n❌ Advanced scraper still not working")
